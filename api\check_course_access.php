<?php
header('Content-Type: application/json');
// CORS headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once __DIR__ . '/../config/config.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/CourseManager.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode([
        'success' => false, 
        'message' => 'يجب تسجيل الدخول أولاً',
        'hasAccess' => false
    ]);
    exit;
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false, 
        'message' => 'طريقة الطلب غير مدعومة',
        'hasAccess' => false
    ]);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['course_id']) || !is_numeric($input['course_id'])) {
    echo json_encode([
        'success' => false, 
        'message' => 'معرف الكورس مطلوب',
        'hasAccess' => false
    ]);
    exit;
}

try {
    $courseManager = new CourseManager();
    $db = Database::getInstance()->getConnection();
    $userId = $_SESSION['user_id'];
    $courseId = $input['course_id'];
    
    // Check if course exists
    $course = $courseManager->getCourseById($courseId);
    if (!$course) {
        echo json_encode([
            'success' => false, 
            'message' => 'الكورس غير موجود',
            'hasAccess' => false
        ]);
        exit;
    }
    
    // Check if user has access
    $hasAccess = $courseManager->userHasAccess($userId, $courseId);
    
    if ($hasAccess) {
        echo json_encode([
            'success' => true, 
            'message' => 'لديك وصول للكورس',
            'hasAccess' => true
        ]);
    } else {
        // Get user course status for more detailed message
        $userCourseStatus = $courseManager->getUserCourseStatus($userId, $courseId);
        
        if ($userCourseStatus) {
            switch ($userCourseStatus['activation_status']) {
                case 'pending':
                    $message = 'اشتراكك في انتظار التفعيل. سيتم تفعيل الكورس خلال 24 ساعة من تأكيد الدفع.';
                    break;
                case 'expired':
                    $message = 'انتهت صلاحية اشتراكك في هذا الكورس. يرجى تجديد الاشتراك.';
                    break;
                case 'rejected':
                    $message = 'تم رفض طلب الاشتراك. يرجى التواصل مع الدعم الفني.';
                    break;
                default:
                    $message = 'ليس لديك صلاحية للوصول لهذا الكورس.';
            }
        } else {
            $message = 'يجب الاشتراك في الكورس أولاً للوصول إلى المحتوى.';
        }
        
        echo json_encode([
            'success' => false, 
            'message' => $message,
            'hasAccess' => false,
            'subscriptionStatus' => $userCourseStatus ? $userCourseStatus['activation_status'] : 'not_subscribed'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Error checking course access: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'حدث خطأ في التحقق من الوصول للكورس',
        'hasAccess' => false
    ]);
}
?>
