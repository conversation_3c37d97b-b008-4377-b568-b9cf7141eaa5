/**
 * Motivational Messages System
 * نظام الرسائل التحفيزية والإرشادية
 */

class MotivationalMessages {
    constructor() {
        this.messages = {
            noAnswers: [
                {
                    title: "لا تيأس! 💪",
                    message: "راجع الفيديو وحل التدريب ثم حل الامتحان",
                    icon: "📚",
                    type: "encouragement"
                },
                {
                    title: "البداية دائماً صعبة 🌱",
                    message: "شاهد الفيديو مرة أخرى وستجد الإجابات",
                    icon: "🎥",
                    type: "guidance"
                },
                {
                    title: "التعلم رحلة 🚀",
                    message: "كل خطوة تقربك من النجاح، ابدأ بمراجعة المحتوى",
                    icon: "✨",
                    type: "motivation"
                }
            ],
            partialAnswers: [
                {
                    title: "أحسنت! تقدم جيد 👏",
                    message: "حل باقي الأسئلة لتحصل على درجة أفضل",
                    icon: "⭐",
                    type: "encouragement"
                },
                {
                    title: "أنت في الطريق الصحيح 🎯",
                    message: "راجع الأسئلة المتبقية وأكمل الحل",
                    icon: "🔍",
                    type: "guidance"
                },
                {
                    title: "لا تتوقف الآن! 🔥",
                    message: "أسئلة قليلة متبقية وستنتهي",
                    icon: "⚡",
                    type: "motivation"
                }
            ],
            wrongAnswers: [
                {
                    title: "لا بأس، الأخطاء تعلمنا 📖",
                    message: "راجع الفيديو وحاول مرة أخرى",
                    icon: "🎓",
                    type: "learning"
                },
                {
                    title: "المحاولة الثانية أفضل 🔄",
                    message: "تعلم من أخطائك وحاول مرة أخرى",
                    icon: "💡",
                    type: "improvement"
                },
                {
                    title: "النجاح يحتاج صبر 🌟",
                    message: "راجع المحتوى وستجد الطريق الصحيح",
                    icon: "🏆",
                    type: "perseverance"
                }
            ],
            timeWarning: [
                {
                    title: "الوقت ينفد! ⏰",
                    message: "ركز على الأسئلة المتبقية",
                    icon: "🚨",
                    type: "urgent"
                },
                {
                    title: "أسرع قليلاً 🏃‍♂️",
                    message: "لديك وقت محدود، اختر أفضل إجابة",
                    icon: "⚡",
                    type: "speed"
                }
            ],
            success: [
                {
                    title: "ممتاز! أحسنت 🎉",
                    message: "لقد أكملت الامتحان بنجاح",
                    icon: "🏆",
                    type: "celebration"
                },
                {
                    title: "مبروك! 🎊",
                    message: "أداء رائع، استمر في التقدم",
                    icon: "⭐",
                    type: "achievement"
                }
            ]
        };
    }

    /**
     * Get random message based on situation
     */
    getMessage(situation, customData = {}) {
        const messages = this.messages[situation] || this.messages.noAnswers;
        const randomMessage = messages[Math.floor(Math.random() * messages.length)];
        
        // Customize message based on data
        if (customData.unansweredCount) {
            randomMessage.message = randomMessage.message.replace(
                'باقي الأسئلة', 
                `${customData.unansweredCount} سؤال متبقي`
            );
        }
        
        if (customData.timeRemaining) {
            randomMessage.message += ` (${customData.timeRemaining} متبقي)`;
        }
        
        return randomMessage;
    }

    /**
     * Show motivational popup
     */
    showMotivationalPopup(situation, customData = {}, duration = 5000) {
        const message = this.getMessage(situation, customData);
        
        const popup = document.createElement('div');
        popup.className = `motivational-popup ${message.type}`;
        popup.innerHTML = `
            <div class="popup-content">
                <div class="popup-icon">${message.icon}</div>
                <div class="popup-text">
                    <h4 class="popup-title">${message.title}</h4>
                    <p class="popup-message">${message.message}</p>
                </div>
                <button class="popup-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;
        
        document.body.appendChild(popup);
        
        // Show with animation
        setTimeout(() => {
            popup.classList.add('show');
        }, 100);
        
        // Auto remove
        setTimeout(() => {
            if (popup.parentElement) {
                popup.classList.remove('show');
                setTimeout(() => {
                    popup.remove();
                }, 300);
            }
        }, duration);
        
        return popup;
    }

    /**
     * Show inactivity reminder
     */
    showInactivityReminder() {
        const reminderMessages = [
            {
                title: "هل تحتاج مساعدة؟ 🤔",
                message: "إذا كنت تواجه صعوبة، راجع الفيديو مرة أخرى",
                icon: "💭"
            },
            {
                title: "لا تتردد في المحاولة 💪",
                message: "كل محاولة تقربك من الفهم الصحيح",
                icon: "🎯"
            },
            {
                title: "خذ وقتك في التفكير 🧠",
                message: "الإجابة الصحيحة تستحق التفكير",
                icon: "💡"
            }
        ];
        
        const randomReminder = reminderMessages[Math.floor(Math.random() * reminderMessages.length)];
        
        return this.showMotivationalPopup('custom', {}, 7000, randomReminder);
    }

    /**
     * Show custom motivational message
     */
    showCustomMessage(messageData, duration = 5000) {
        const popup = document.createElement('div');
        popup.className = `motivational-popup ${messageData.type || 'info'}`;
        popup.innerHTML = `
            <div class="popup-content">
                <div class="popup-icon">${messageData.icon || '💡'}</div>
                <div class="popup-text">
                    <h4 class="popup-title">${messageData.title}</h4>
                    <p class="popup-message">${messageData.message}</p>
                </div>
                <button class="popup-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;
        
        document.body.appendChild(popup);
        
        // Show with animation
        setTimeout(() => {
            popup.classList.add('show');
        }, 100);
        
        // Auto remove
        setTimeout(() => {
            if (popup.parentElement) {
                popup.classList.remove('show');
                setTimeout(() => {
                    popup.remove();
                }, 300);
            }
        }, duration);
        
        return popup;
    }

    /**
     * Setup inactivity detection
     */
    setupInactivityDetection(timeoutMinutes = 5) {
        let inactivityTimer;
        let lastActivity = Date.now();
        
        const resetTimer = () => {
            lastActivity = Date.now();
            clearTimeout(inactivityTimer);
            
            inactivityTimer = setTimeout(() => {
                this.showInactivityReminder();
            }, timeoutMinutes * 60 * 1000);
        };
        
        // Track user activity
        const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
        
        events.forEach(event => {
            document.addEventListener(event, resetTimer, true);
        });
        
        // Start timer
        resetTimer();
    }

    /**
     * Show result-based message
     */
    showResultMessage(score, totalQuestions, passed) {
        let situation, customMessage;
        
        if (score === 0) {
            situation = 'noAnswers';
        } else if (score < totalQuestions * 0.6) {
            situation = 'wrongAnswers';
            customMessage = {
                title: "يمكنك تحسين أدائك 📈",
                message: `حصلت على ${score} من ${totalQuestions}. راجع المحتوى وحاول مرة أخرى`,
                icon: "📚",
                type: "improvement"
            };
        } else if (passed) {
            situation = 'success';
            customMessage = {
                title: "مبروك! نجحت 🎉",
                message: `حصلت على ${score} من ${totalQuestions}. أداء ممتاز!`,
                icon: "🏆",
                type: "celebration"
            };
        } else {
            situation = 'partialAnswers';
        }
        
        if (customMessage) {
            return this.showCustomMessage(customMessage, 8000);
        } else {
            return this.showMotivationalPopup(situation, { score, totalQuestions }, 8000);
        }
    }
}

// Initialize global instance
window.motivationalMessages = new MotivationalMessages();

// CSS Styles for motivational popups
const motivationalStyles = `
<style>
.motivational-popup {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%) translateY(-100px);
    z-index: 9999;
    opacity: 0;
    transition: all 0.3s ease;
    max-width: 400px;
    width: 90%;
}

.motivational-popup.show {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
}

.popup-content {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    display: flex;
    align-items: center;
    gap: 15px;
    position: relative;
    border-left: 5px solid #007bff;
}

.motivational-popup.encouragement .popup-content {
    border-left-color: #28a745;
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
}

.motivational-popup.guidance .popup-content {
    border-left-color: #17a2b8;
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
}

.motivational-popup.motivation .popup-content {
    border-left-color: #ffc107;
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
}

.motivational-popup.urgent .popup-content {
    border-left-color: #dc3545;
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    animation: shake 0.5s ease-in-out;
}

.popup-icon {
    font-size: 32px;
    flex-shrink: 0;
}

.popup-text {
    flex: 1;
}

.popup-title {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: bold;
    color: #333;
}

.popup-message {
    margin: 0;
    font-size: 14px;
    color: #666;
    line-height: 1.4;
}

.popup-close {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    font-size: 20px;
    color: #999;
    cursor: pointer;
    width: 25px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.popup-close:hover {
    background: rgba(0,0,0,0.1);
    color: #333;
}

@keyframes shake {
    0%, 100% { transform: translateX(-50%) translateY(0) rotate(0deg); }
    25% { transform: translateX(-50%) translateY(0) rotate(1deg); }
    75% { transform: translateX(-50%) translateY(0) rotate(-1deg); }
}
</style>
`;

// Add styles to document
document.head.insertAdjacentHTML('beforeend', motivationalStyles);
