/**
 * Enhanced Exam System with Smart Warnings and Confirmations
 * نظام الامتحانات المحسن مع التحذيرات والتأكيدات الذكية
 */

class EnhancedExamSystem {
    constructor() {
        this.timer = null;
        this.remainingSeconds = 0;
        this.warningsShown = {
            fifteen: false,
            sixty: false,
            fiveMinutes: false,
            oneMinute: false
        };
        this.examAnswers = {};
        this.totalQuestions = 0;
        this.isAutoSubmit = false;
    }

    /**
     * Initialize the exam system
     * @param {number} durationMinutes - Duration in minutes
     * @param {number} totalQuestions - Total number of questions
     */
    init(durationMinutes, totalQuestions) {
        this.remainingSeconds = durationMinutes * 60;
        this.totalQuestions = totalQuestions;
        this.createTimerDisplay();
        this.startTimer();
        this.setupAnswerTracking();
        this.setupSubmitConfirmation();
    }

    /**
     * Create enhanced timer display
     */
    createTimerDisplay() {
        const timerHTML = `
            <div id="enhancedExamTimer" class="enhanced-timer">
                <div class="timer-icon">⏰</div>
                <div class="timer-content">
                    <div class="timer-label">الوقت المتبقي</div>
                    <div class="timer-display" id="timerDisplay">00:00</div>
                    <div class="timer-progress">
                        <div class="progress-bar" id="timerProgressBar"></div>
                    </div>
                </div>
            </div>
        `;
        
        // Add to page if not exists
        if (!document.getElementById('enhancedExamTimer')) {
            document.body.insertAdjacentHTML('afterbegin', timerHTML);
        }
    }

    /**
     * Start the countdown timer
     */
    startTimer() {
        this.timer = setInterval(() => {
            this.remainingSeconds--;
            this.updateTimerDisplay();
            this.checkWarnings();
            
            if (this.remainingSeconds <= 0) {
                this.handleTimeUp();
            }
        }, 1000);
    }

    /**
     * Update timer display
     */
    updateTimerDisplay() {
        const minutes = Math.floor(this.remainingSeconds / 60);
        const seconds = this.remainingSeconds % 60;
        const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        
        const timerDisplay = document.getElementById('timerDisplay');
        const progressBar = document.getElementById('timerProgressBar');
        
        if (timerDisplay) {
            timerDisplay.textContent = timeString;
        }
        
        // Update progress bar
        if (progressBar) {
            const totalSeconds = this.remainingSeconds + (this.warningsShown.fifteen ? 15 : 0);
            const progress = (this.remainingSeconds / totalSeconds) * 100;
            progressBar.style.width = `${progress}%`;
            
            // Change color based on time remaining
            if (this.remainingSeconds <= 15) {
                progressBar.style.backgroundColor = '#dc3545'; // Red
                timerDisplay.style.color = '#dc3545';
            } else if (this.remainingSeconds <= 60) {
                progressBar.style.backgroundColor = '#fd7e14'; // Orange
                timerDisplay.style.color = '#fd7e14';
            } else if (this.remainingSeconds <= 300) {
                progressBar.style.backgroundColor = '#ffc107'; // Yellow
                timerDisplay.style.color = '#ffc107';
            }
        }
    }

    /**
     * Check for warnings at specific time intervals
     */
    checkWarnings() {
        // 15 seconds warning - NEW!
        if (this.remainingSeconds === 15 && !this.warningsShown.fifteen) {
            this.showUrgentWarning();
            this.warningsShown.fifteen = true;
        }
        
        // 1 minute warning
        if (this.remainingSeconds === 60 && !this.warningsShown.sixty) {
            this.showCriticalWarning();
            this.warningsShown.sixty = true;
        }
        
        // 5 minutes warning
        if (this.remainingSeconds === 300 && !this.warningsShown.fiveMinutes) {
            this.showTimeWarning('تحذير!', 'يتبقى 5 دقائق فقط لانتهاء الامتحان', 'warning');
            this.warningsShown.fiveMinutes = true;
        }
        
        // 1 minute warning
        if (this.remainingSeconds === 60 && !this.warningsShown.oneMinute) {
            this.showTimeWarning('تحذير هام!', 'يتبقى دقيقة واحدة فقط لانتهاء الامتحان', 'critical');
            this.warningsShown.oneMinute = true;
        }
    }

    /**
     * Show urgent 15-second warning
     */
    showUrgentWarning() {
        const modal = this.createWarningModal(
            '⚡ إنذار عاجل!',
            'لا يوجد وقت! بادر في تسليم الامتحان',
            'urgent',
            '15 ثانية متبقية فقط!'
        );
        
        // Auto-hide after 3 seconds
        setTimeout(() => {
            if (modal.parentElement) {
                modal.remove();
            }
        }, 3000);
    }

    /**
     * Show critical 1-minute warning
     */
    showCriticalWarning() {
        const modal = this.createWarningModal(
            '🚨 تحذير حرج!',
            'دقيقة واحدة فقط متبقية! راجع إجاباتك بسرعة',
            'critical',
            'دقيقة واحدة متبقية'
        );
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            if (modal.parentElement) {
                modal.remove();
            }
        }, 5000);
    }

    /**
     * Create warning modal
     */
    createWarningModal(title, message, type, timeText) {
        const modal = document.createElement('div');
        modal.className = `exam-warning-modal ${type}`;
        modal.innerHTML = `
            <div class="warning-content">
                <div class="warning-header">
                    <div class="warning-icon ${type}">
                        ${type === 'urgent' ? '⚡' : type === 'critical' ? '🚨' : '⚠️'}
                    </div>
                    <h3 class="warning-title">${title}</h3>
                </div>
                <div class="warning-body">
                    <p class="warning-message">${message}</p>
                    <div class="warning-time ${type}">${timeText}</div>
                </div>
                <div class="warning-actions">
                    <button class="warning-btn" onclick="this.closest('.exam-warning-modal').remove()">
                        فهمت
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Add animation
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
        
        return modal;
    }

    /**
     * Handle time up
     */
    handleTimeUp() {
        clearInterval(this.timer);
        this.isAutoSubmit = true;
        
        this.showTimeUpModal();
    }

    /**
     * Show time up modal
     */
    showTimeUpModal() {
        const modal = document.createElement('div');
        modal.className = 'time-up-modal';
        modal.innerHTML = `
            <div class="time-up-content">
                <div class="time-up-icon">⏰</div>
                <h2>انتهى الوقت!</h2>
                <p>سيتم إرسال الامتحان تلقائياً.</p>
                <div class="countdown">3</div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Countdown before auto-submit
        let countdown = 3;
        const countdownElement = modal.querySelector('.countdown');
        
        const countdownTimer = setInterval(() => {
            countdown--;
            countdownElement.textContent = countdown;
            
            if (countdown <= 0) {
                clearInterval(countdownTimer);
                this.autoSubmitExam();
            }
        }, 1000);
    }

    /**
     * Setup answer tracking
     */
    setupAnswerTracking() {
        // Track all form inputs
        document.addEventListener('change', (e) => {
            if (e.target.type === 'radio' || e.target.type === 'checkbox' || e.target.tagName === 'SELECT') {
                this.trackAnswer(e.target);
            }
        });
        
        document.addEventListener('input', (e) => {
            if (e.target.type === 'text' || e.target.tagName === 'TEXTAREA') {
                this.trackAnswer(e.target);
            }
        });
    }

    /**
     * Track individual answer
     */
    trackAnswer(element) {
        const questionId = this.getQuestionId(element);
        if (questionId) {
            this.examAnswers[questionId] = element.value;
        }
    }

    /**
     * Get question ID from form element
     */
    getQuestionId(element) {
        // Try to find question ID from name attribute or closest question container
        if (element.name) {
            const match = element.name.match(/answers?\[(\d+)\]/) || element.name.match(/question_(\d+)/);
            if (match) {
                return match[1];
            }
        }
        
        // Try to find from closest question container
        const questionContainer = element.closest('[data-question-id]');
        if (questionContainer) {
            return questionContainer.dataset.questionId;
        }
        
        return null;
    }

    /**
     * Setup submit confirmation
     */
    setupSubmitConfirmation() {
        // Override form submission
        const forms = document.querySelectorAll('form[id*="exam"], form[id*="exercise"], form[id*="test"]');

        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                if (!this.isAutoSubmit) {
                    e.preventDefault();
                    this.showSmartConfirmation(form);
                }
            });
        });

        // Override submit buttons
        const submitButtons = document.querySelectorAll('button[onclick*="submit"], input[type="submit"]');

        submitButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                if (!this.isAutoSubmit) {
                    e.preventDefault();
                    const form = button.closest('form');
                    if (form) {
                        this.showSmartConfirmation(form);
                    }
                }
            });
        });
    }

    /**
     * Show smart confirmation based on answer status
     */
    showSmartConfirmation(form) {
        const answerStatus = this.analyzeAnswers();
        const confirmationData = this.getConfirmationMessage(answerStatus);

        this.showConfirmationModal(confirmationData, () => {
            this.isAutoSubmit = true;
            form.submit();
        });
    }

    /**
     * Analyze current answers
     */
    analyzeAnswers() {
        const allInputs = document.querySelectorAll('input[type="radio"]:checked, input[type="checkbox"]:checked, select, input[type="text"], textarea');
        const answeredQuestions = new Set();
        let totalAnswered = 0;
        let hasTextAnswers = false;

        allInputs.forEach(input => {
            const questionId = this.getQuestionId(input);
            if (questionId && input.value.trim() !== '') {
                answeredQuestions.add(questionId);
                totalAnswered++;

                if (input.type === 'text' || input.tagName === 'TEXTAREA') {
                    hasTextAnswers = true;
                }
            }
        });

        // Try to get total questions from form or page
        const totalQuestions = this.getTotalQuestions();

        return {
            totalQuestions,
            answeredQuestions: answeredQuestions.size,
            totalAnswered,
            hasTextAnswers,
            percentageAnswered: totalQuestions > 0 ? (answeredQuestions.size / totalQuestions) * 100 : 0
        };
    }

    /**
     * Get total number of questions
     */
    getTotalQuestions() {
        // Try different methods to get total questions
        const questionContainers = document.querySelectorAll('.question, [data-question-id], .exam-question, .exercise-question');
        if (questionContainers.length > 0) {
            return questionContainers.length;
        }

        // Try to count radio button groups
        const radioGroups = new Set();
        document.querySelectorAll('input[type="radio"]').forEach(radio => {
            if (radio.name) {
                radioGroups.add(radio.name);
            }
        });

        if (radioGroups.size > 0) {
            return radioGroups.size;
        }

        // Fallback to stored total
        return this.totalQuestions || 10;
    }

    /**
     * Get appropriate confirmation message based on answer status
     */
    getConfirmationMessage(answerStatus) {
        const { totalQuestions, answeredQuestions, percentageAnswered } = answerStatus;

        // No answers at all
        if (answeredQuestions === 0) {
            return {
                title: '⚠️ لم تحل أي سؤال!',
                message: 'لا تيأس! راجع الفيديو وحل التدريب ثم حل الامتحان مرة أخرى.',
                type: 'no-answers',
                confirmText: 'تسليم على أي حال',
                cancelText: 'العودة للحل',
                showMotivation: true
            };
        }

        // Partial answers
        if (percentageAnswered < 100) {
            const unanswered = totalQuestions - answeredQuestions;
            return {
                title: '🤔 لم تكمل جميع الأسئلة',
                message: `لديك ${unanswered} سؤال لم تجب عليه. هل أنت متأكد من هذه الإجابات؟`,
                type: 'partial-answers',
                confirmText: 'نعم، أريد التسليم',
                cancelText: 'العودة للمراجعة',
                showMotivation: false
            };
        }

        // All questions answered
        return {
            title: '✅ ممتاز!',
            message: 'لقد أجبت على جميع الأسئلة. هل أنت متأكد من إجاباتك؟',
            type: 'all-answered',
            confirmText: 'نعم، تسليم الامتحان',
            cancelText: 'مراجعة الإجابات',
            showMotivation: false
        };
    }

    /**
     * Show confirmation modal
     */
    showConfirmationModal(data, onConfirm) {
        const modal = document.createElement('div');
        modal.className = `smart-confirmation-modal ${data.type}`;

        const motivationSection = data.showMotivation ? `
            <div class="motivation-section">
                <div class="motivation-icon">💪</div>
                <p class="motivation-text">لا تستسلم! المحاولة والتعلم جزء من النجاح</p>
            </div>
        ` : '';

        modal.innerHTML = `
            <div class="confirmation-overlay"></div>
            <div class="confirmation-content">
                <div class="confirmation-header">
                    <h3 class="confirmation-title">${data.title}</h3>
                </div>
                <div class="confirmation-body">
                    <p class="confirmation-message">${data.message}</p>
                    ${motivationSection}
                </div>
                <div class="confirmation-actions">
                    <button class="confirmation-btn cancel-btn" onclick="this.closest('.smart-confirmation-modal').remove()">
                        ${data.cancelText}
                    </button>
                    <button class="confirmation-btn confirm-btn" id="confirmSubmit">
                        ${data.confirmText}
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Add event listeners
        modal.querySelector('#confirmSubmit').addEventListener('click', () => {
            modal.remove();
            onConfirm();
        });

        // Show modal with animation
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
    }

    /**
     * Auto submit exam when time is up
     */
    autoSubmitExam() {
        // Find and submit the exam form
        const examForm = document.querySelector('form[id*="exam"], form[id*="exercise"], form[id*="test"]');
        if (examForm) {
            this.isAutoSubmit = true;
            examForm.submit();
        } else {
            // Try to find submit button and click it
            const submitBtn = document.querySelector('button[onclick*="submit"]');
            if (submitBtn) {
                this.isAutoSubmit = true;
                submitBtn.click();
            }
        }
    }
}

// Initialize global instance
window.enhancedExamSystem = new EnhancedExamSystem();

// CSS Styles
const styles = `
<style>
.enhanced-timer {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 10px;
    min-width: 200px;
}

.timer-icon {
    font-size: 24px;
}

.timer-content {
    flex: 1;
}

.timer-label {
    font-size: 12px;
    opacity: 0.8;
    margin-bottom: 5px;
}

.timer-display {
    font-size: 20px;
    font-weight: bold;
    font-family: 'Courier New', monospace;
}

.timer-progress {
    height: 4px;
    background: rgba(255,255,255,0.3);
    border-radius: 2px;
    margin-top: 8px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: #28a745;
    transition: width 1s ease, background-color 0.3s ease;
}

.exam-warning-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.exam-warning-modal.show {
    opacity: 1;
}

.warning-content {
    background: white;
    border-radius: 20px;
    padding: 30px;
    text-align: center;
    max-width: 400px;
    width: 90%;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

.exam-warning-modal.show .warning-content {
    transform: scale(1);
}

.warning-header {
    margin-bottom: 20px;
}

.warning-icon {
    font-size: 48px;
    margin-bottom: 15px;
}

.warning-icon.urgent {
    animation: flash 0.5s infinite;
}

.warning-icon.critical {
    animation: pulse 1s infinite;
}

.warning-title {
    font-size: 24px;
    margin: 0;
    color: #333;
}

.warning-message {
    font-size: 16px;
    color: #666;
    margin-bottom: 15px;
    line-height: 1.5;
}

.warning-time {
    font-size: 18px;
    font-weight: bold;
    padding: 10px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.warning-time.urgent {
    background: #dc3545;
    color: white;
    animation: flash 0.5s infinite;
}

.warning-time.critical {
    background: #fd7e14;
    color: white;
}

.warning-time.warning {
    background: #ffc107;
    color: #333;
}

.warning-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 16px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.warning-btn:hover {
    background: #0056b3;
}

.time-up-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(220, 53, 69, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.time-up-content {
    background: white;
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
}

.time-up-icon {
    font-size: 64px;
    margin-bottom: 20px;
}

.time-up-content h2 {
    color: #dc3545;
    margin-bottom: 15px;
}

.countdown {
    font-size: 48px;
    font-weight: bold;
    color: #dc3545;
    margin-top: 20px;
}

@keyframes flash {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Smart Confirmation Modal Styles */
.smart-confirmation-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.smart-confirmation-modal.show {
    opacity: 1;
}

.confirmation-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.7);
}

.confirmation-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.8);
    background: white;
    border-radius: 20px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    transition: transform 0.3s ease;
}

.smart-confirmation-modal.show .confirmation-content {
    transform: translate(-50%, -50%) scale(1);
}

.confirmation-header {
    text-align: center;
    margin-bottom: 20px;
}

.confirmation-title {
    font-size: 24px;
    margin: 0;
    color: #333;
}

.no-answers .confirmation-title {
    color: #dc3545;
}

.partial-answers .confirmation-title {
    color: #ffc107;
}

.all-answered .confirmation-title {
    color: #28a745;
}

.confirmation-message {
    font-size: 16px;
    color: #666;
    text-align: center;
    line-height: 1.6;
    margin-bottom: 20px;
}

.motivation-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    margin: 20px 0;
}

.motivation-icon {
    font-size: 32px;
    margin-bottom: 10px;
}

.motivation-text {
    font-size: 16px;
    margin: 0;
    font-weight: 500;
}

.confirmation-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 25px;
}

.confirmation-btn {
    padding: 12px 25px;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

.cancel-btn {
    background: #6c757d;
    color: white;
}

.cancel-btn:hover {
    background: #545b62;
}

.confirm-btn {
    background: #007bff;
    color: white;
}

.confirm-btn:hover {
    background: #0056b3;
}

.no-answers .confirm-btn {
    background: #dc3545;
}

.no-answers .confirm-btn:hover {
    background: #c82333;
}

.partial-answers .confirm-btn {
    background: #ffc107;
    color: #333;
}

.partial-answers .confirm-btn:hover {
    background: #e0a800;
}

.all-answered .confirm-btn {
    background: #28a745;
}

.all-answered .confirm-btn:hover {
    background: #1e7e34;
}
</style>
`;

// Add styles to document
document.head.insertAdjacentHTML('beforeend', styles);
